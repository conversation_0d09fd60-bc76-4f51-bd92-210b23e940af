/**
 * 配置工具模块
 * 负责处理环境变量和配置信息
 */
var dotenv = require("dotenv");
// 加载环境变量
dotenv.config();
/**
 * 检查必要的环境变量是否已配置
 * @throws 如果必需的环境变量未设置，则抛出错误
 */
function validateEnv() {
    // 使用本地Ollama服务，无需API密钥验证
    console.log("使用本地Ollama服务，跳过API密钥验证");
    return true;
}
/**
 * 获取 Deepseek API 密钥
 * @returns Deepseek API密钥
 */
function getApiKey() {
    // 本地Ollama服务不需要API密钥，返回占位符
    return process.env.DEEPSEEK_API_KEY || "ollama-local";
}
/**
 * 获取配置的LLM模型名称
 * 如果环境变量中未指定，则使用默认值
 * @returns 模型名称
 */
function getModelName() {
    return process.env.MODEL_NAME || "qwen2.5:7b";
}
function getBaseURL() {
    return process.env.BASE_URL || "http://60.29.78.246:11434/v1";
}
/**
 * 默认配置
 */
var defaultConfig = {
    clientName: "mcp-client-cli",
    clientVersion: "1.0.0",
    defaultModel: "qwen2.5:7b"
};
module.exports = {
    validateEnv: validateEnv,
    getApiKey: getApiKey,
    getModelName: getModelName,
    getBaseURL: getBaseURL,
    defaultConfig: defaultConfig
};
