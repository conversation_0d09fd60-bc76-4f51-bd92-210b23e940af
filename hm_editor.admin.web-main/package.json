{"name": "hmEditor.admin.web", "version": "0.0.0", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve --disable-host-check --port 23071 --public-host=http://127.0.0.1:23071/ --base-href=/hmEditor/admin-client/ --deploy-url=/hmEditor/admin-client/", "build": "ng build -aot -prod --base-href=/hmEditor/admin-client/ --deploy-url=/hmEditor/admin-client/ ", "create": "ng g ", "server": "http-server hmEditor.admin.web -c-1 --cors"}, "private": true, "dependencies": {"@angular/animations": "^4.0.0", "@angular/common": "^4.0.0", "@angular/compiler": "^4.0.0", "@angular/core": "^4.0.0", "@angular/forms": "^4.0.0", "@angular/http": "^4.0.0", "@angular/platform-browser": "^4.0.0", "@angular/platform-browser-dynamic": "^4.0.0", "@angular/router": "^4.0.0", "@types/jquery": "^3.2.12", "bson-objectid": "^1.3.1", "core-js": "^2.4.1", "date-fns": "^2.29.3", "date-fns-tz": "^1.3.7", "ejs": "^2.7.4", "font-awesome": "^4.7.0", "jquery": "^3.2.1", "jquery.json-viewer": "^1.5.0", "ng2-file-upload": "1.3.0", "primeng": "^4.1.2", "rxjs": "^5.4.2", "underscore": "^1.10.2"}, "devDependencies": {"@angular/cli": "~1.4.7", "@angular/compiler-cli": "^4.0.0", "@angular/language-service": "^4.0.0", "@types/jasmine": "~2.5.53", "@types/jasminewd2": "~2.0.2", "@types/jquery": "^3.2.12", "@types/node": "~6.0.60", "codelyzer": "~3.0.1", "http-server": "~0.9.0", "jasmine-core": "~2.6.2", "jquery": "^3.2.1", "node-sass": "^7.0.3", "sass": "^1.89.2", "ts-node": "~3.0.4", "tslint": "~5.3.2", "typescript": "~2.3.3"}}